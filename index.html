<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkillVerdict | Monetize Your Expertise as a Developer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #f59e0b;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --success: #10b981;
            --vetter-bg: #eff6ff;
            --candidate-bg: #f0fdf4;
            --reputation: #7c3aed;
            --unverified: #f59e0b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            color: var(--dark);
            background-color: var(--light);
            line-height: 1.6;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header & Navigation */
        header {
            background-color: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
        }

        .logo i {
            margin-right: 8px;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        .cta-button {
            background-color: var(--primary);
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s;
        }

        .cta-button:hover {
            background-color: var(--primary-dark);
        }

        /* Hero Section */
        .hero {
            padding: 150px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.5rem;
            color: var(--gray);
            max-width: 800px;
            margin: 0 auto 40px;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .hero-btn {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: white;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn-secondary:hover {
            background-color: var(--primary);
            color: white;
            transform: translateY(-2px);
        }

        .signup-form {
            max-width: 500px;
            margin: 40px auto 0;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-input {
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            width: 100%;
        }

        .form-input:focus {
            border-color: var(--primary);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .submit-button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .submit-button:hover {
            background-color: var(--primary-dark);
        }

        /* Trust Badges */
        .trust-badges {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .trust-badge {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .trust-badge i {
            color: var(--success);
            font-size: 1.2rem;
        }

        /* Sections */
        section {
            padding: 80px 0;
        }

        section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: var(--dark);
        }

        /* Two Sides Section - Redesigned */
        .two-sides {
            background-color: white;
            padding: 80px 0;
        }

        .two-sides .reputation-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 50px;
            font-size: 1.2rem;
            color: var(--gray);
        }

        .tabs-container {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            border-bottom: 1px solid #e5e7eb;
        }

        .tab {
            padding: 15px 30px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tab.active {
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
        }

        .tab i {
            font-size: 1.3rem;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: flex;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .side-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .side-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: var(--primary);
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .side-description {
            font-size: 1.2rem;
            color: var(--gray);
            margin-bottom: 40px;
            max-width: 600px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            width: 100%;
        }

        .benefit-card {
            background: var(--light);
            padding: 25px;
            border-radius: 12px;
            text-align: left;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 4px solid var(--primary);
        }

        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .benefit-icon {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .benefit-title {
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1rem;
            color: var(--dark);
        }

        .benefit-description {
            color: var(--gray);
            font-size: 0.95rem;
        }

        /* Stats Section */
        .stats {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 60px 0;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
        }

        .stat {
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--gray);
        }

        /* CV2.0 Preview Section */
        .cv-preview {
            background-color: white;
        }

        .cv-showcase {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .cv-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 30px;
            display: flex;
            align-items: center;
        }

        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            background: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 30px;
            font-size: 3rem;
            color: var(--primary);
        }

        .profile-info h2 {
            font-size: 2rem;
            margin-bottom: 5px;
            text-align: left;
            color: white;
        }

        .profile-info p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .cv-body {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .cv-section {
            margin-bottom: 25px;
        }

        .cv-section-title {
            font-size: 1.4rem;
            color: var(--primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e0f2fe;
            display: flex;
            align-items: center;
        }

        .cv-section-title i {
            margin-right: 10px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .skill {
            background: #f8fafc;
            padding: 12px 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-left: 4px solid var(--primary);
            position: relative;
            cursor: pointer;
        }

        .skill-name {
            font-weight: 500;
        }

        .verified-badge {
            background: var(--success);
            color: white;
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 50px;
            display: flex;
            align-items: center;
        }

        .unverified-badge {
            background: var(--unverified);
            color: white;
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 50px;
            display: flex;
            align-items: center;
        }

        .verified-badge i,
        .unverified-badge i {
            margin-right: 5px;
            font-size: 0.7rem;
        }

        .experience-item,
        .education-item {
            margin-bottom: 20px;
            position: relative;
            padding-right: 100px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .item-title {
            font-weight: 600;
            color: var(--dark);
        }

        .item-date {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .item-subtitle {
            color: var(--primary);
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .item-description {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .verification-status {
            position: absolute;
            right: 0;
            top: 0;
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 50px;
            display: flex;
            align-items: center;
        }

        .status-verified {
            background: var(--success);
            color: white;
        }

        .status-unverified {
            background: var(--unverified);
            color: white;
        }

        .status-pending {
            background: var(--unverified);
            color: white;
        }

        .qr-section {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e0f2fe;
        }

        .qr-code {
            width: 120px;
            height: 120px;
            background: #ddd;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--gray);
        }

        .verification-link {
            color: var(--primary);
            text-decoration: none;
            font-size: 0.9rem;
            display: inline-block;
            margin-top: 10px;
        }

        .verification-link:hover {
            text-decoration: underline;
        }

        .cv-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .cv-feature {
            text-align: center;
            padding: 20px;
        }

        .cv-feature i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 15px;
        }

        /* Tooltip for verification details */
        .skill-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            width: 220px;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .skill:hover .skill-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .skill-tooltip:after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 8px solid transparent;
            border-top-color: var(--dark);
        }

        .verifier-info {
            margin-top: 8px;
            font-size: 0.8rem;
            color: #93c5fd;
        }

        .verifier-reputation {
            display: inline-block;
            background: var(--reputation);
            padding: 2px 8px;
            border-radius: 20px;
            margin-top: 5px;
            font-size: 0.7rem;
        }

        /* Project items styling */
        .project-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
            position: relative;
        }

        .project-item .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .project-item .item-title {
            font-weight: 600;
            color: var(--dark);
            font-size: 1.1rem;
        }

        .project-item .item-subtitle {
            color: var(--primary);
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .project-item .item-description {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 10px;
        }

        /* Tooltip for project verification details */
        .project-item .skill-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 0.9rem;
            width: 220px;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .project-item:hover .skill-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .project-item .skill-tooltip:after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 8px solid transparent;
            border-top-color: var(--dark);
        }

        /* How It Works */
        .process {
            background-color: white;
        }

        .process-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            border-bottom: 1px solid #ddd;
        }

        .process-tab {
            padding: 15px 30px;
            cursor: pointer;
            font-weight: 600;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .process-tab.active {
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
        }

        .process-content {
            display: none;
        }

        .process-content.active {
            display: block;
        }

        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .step {
            text-align: center;
            padding: 30px 20px;
        }

        .step .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary);
        }

        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        /* FAQ Section */
        .faq {
            background-color: white;
            padding: 80px 0;
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-question {
            padding: 20px;
            background-color: var(--light);
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .faq-question:hover {
            background-color: #e6f0ff;
        }

        .faq-question i {
            transition: transform 0.3s ease;
            color: var(--primary);
        }

        .faq-item.active .faq-question i {
            transform: rotate(180deg);
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            background-color: white;
        }

        .faq-item.active .faq-answer {
            max-height: 500px;
            padding: 20px;
            border-top: 1px solid #eee;
        }

        /* Final CTA */
        .final-cta {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        .final-cta h2 {
            margin-bottom: 20px;
        }

        .final-cta p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto 40px;
        }

        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        footer a {
            color: #93c5fd;
            text-decoration: none;
            transition: color 0.3s;
        }

        footer a:hover {
            color: white;
        }

        .social-links {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.2rem;
            }

            .email-form {
                flex-direction: column;
            }

            .email-input {
                border-radius: 50px;
                margin-bottom: 10px;
            }

            .submit-button {
                border-radius: 50px;
            }

            .nav-links {
                display: none;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-btn {
                width: 100%;
                text-align: center;
            }

            .trust-badges {
                flex-direction: column;
            }

            .cv-body {
                grid-template-columns: 1fr;
            }

            .cv-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-img {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }

            .experience-item,
            .education-item {
                padding-right: 0;
                padding-bottom: 40px;
            }

            .verification-status {
                top: auto;
                bottom: 10px;
                right: 0;
            }

            .faq-question {
                padding: 15px;
            }

            .faq-answer {
                padding: 0 15px;
            }

            .faq-item.active .faq-answer {
                padding: 15px;
            }

            .skill-tooltip {
                width: 180px;
                font-size: 0.8rem;
            }

            .project-item .item-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .project-item .verified-badge,
            .project-item .unverified-badge {
                margin-top: 8px;
            }

            .project-item .skill-tooltip {
                width: 180px;
                font-size: 0.8rem;
            }

            /* Responsive Design for Two Sides Section */
            .benefits-grid {
                grid-template-columns: 1fr;
            }

            .tab {
                padding: 12px 20px;
                font-size: 1rem;
            }

            .side-icon {
                width: 100px;
                height: 100px;
                font-size: 3rem;
            }
        }
    </style>
</head>

<body>
    <!-- Header & Navigation -->
    <header>
        <div class="container">
            <nav>
                <div class="logo">
                    <i class="fas fa-certificate"></i>
                    SkillVerdict
                </div>
                <div class="nav-links">
                    <a href="#home">Home</a>
                    <a href="#how-it-works">How It Works</a>
                    <a href="#faq">FAQ</a>
                    <a href="#waitlist" class="cta-button">Join Waitlist</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>Don't let your CV be ignored by AI and HR, get your CV2.0 today
            </h1>
            <p>SkillVerdict is a decentralized, unbiased and respected community
                where global tech professionals from leading
                companies verify your
                skills. Replace empty resume claims with proven endorsements, build
                your CV2.0 and get hired faster, or earn
                income by selling your own credibility as a tech professional</p>

            <div class="hero-buttons">
                <a href="#for-candidates" class="hero-btn btn-secondary">Verify
                    my skills</a>
                <a href="#for-vetters" class="hero-btn btn-primary">
                    Earn by verifying</a>
            </div>

            <form class="signup-form">
                <input type="email" class="form-input" placeholder="Your email address *" required>
                <input type="url" class="form-input" placeholder="Your LinkedIn profile URL *" required>
                <textarea class="form-input form-textarea"
                    placeholder="Biggest pain point with job hunting (optional)"></textarea>
                <button type="submit" class="submit-button">Secure my spot</button>
            </form>

            <div class="trust-badges">
                <div class="trust-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>100% Secure & transparent</span>
                </div>
                <div class="trust-badge">
                    <i class="fas fa-clock"></i>
                    <span>No time commitment</span>
                </div>
                <div class="trust-badge">
                    <i class="fas fa-user-check"></i>
                    <span>Verified by world's top talent</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Two Sides Section - Redesigned -->
    <section class="two-sides">
        <div class="container">
            <h2>Twitter Community Notes, but for your skills</h2>
            <p class="reputation-intro">Inspired by models like Twitter's Community Notes, SkillVerdict brings
                transparent, community-driven verification to member's skills. It’s unbiased assessment, powered by the
                community.</p>

            <div class="tabs-container">
                <div class="tab active" data-tab="vetter">
                    <i class="fas fa-money-bill-wave"></i> Tech professions
                </div>
                <div class="tab" data-tab="candidate">
                    <i class="fas fa-award"></i> Members
                </div>
            </div>

            <div class="tab-content active" id="vetter-content">
                <div class="side-content">
                    <div class="side-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <p class="side-description">Top talent is valued in the
                        SkillVerdict community. Monetize your
                        expertise by verifying the skills of other members.</p>

                    <div class="benefits-grid">
                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="benefit-title">Earn extra income</div>
                            <div class="benefit-description">Get paid for reviewing code and conducting technical
                                interviews</div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="benefit-title">Flexible schedule</div>
                            <div class="benefit-description">Work on your own time from anywhere - no contracts or
                                minimum hours</div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="benefit-title">Build your reputation</div>
                            <div class="benefit-description">Establish yourself as an industry expert in the community
                            </div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="benefit-title">Expand your network</div>
                            <div class="benefit-description">Connect with
                                talented tech professionals</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="candidate-content">
                <div class="side-content">
                    <div class="side-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <p class="side-description">Get your skills verified by global industry experts and boost your
                        career with a next-generation CV2.0.</p>

                    <div class="benefits-grid">
                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div class="benefit-title">Verified skill badges</div>
                            <div class="benefit-description">Stand out with verified skill badges on your profile</div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-forward"></i>
                            </div>
                            <div class="benefit-title">Possibly skip lengthy interviews</div>
                            <div class="benefit-description">Chance to reduce multi-step
                                interviews or skip fully because your skills are already
                                verified by world's top talent</div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="benefit-title">Increase Hiring Chances</div>
                            <div class="benefit-description">Don't just say
                                you're good—prove it. Verified skills from
                                top professionals make employers notice</div>
                        </div>

                        <div class="benefit-card">
                            <div class="benefit-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="benefit-title">Customizable digital CV2.0</div>
                            <div class="benefit-description">Share your verified
                                skills instantly. Export a digital CV2.0
                                with a trusted QR code or send a live link to anyone.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="process" id="how-it-works">
        <div class="container">
            <h2>How SkillVerdict Works</h2>
            <p class="reputation-intro">
                Our process ensures rigorous verification while maintaining
                flexibility for both tech professionals and members. We hand
                pick our verifiers, you will be assured that your skills are
                going to be verified in the upmost professional manner,
                unbiased, professional, fast and transparent.
            </p>

            <div class="process-tabs">
                <div class="process-tab active" data-tab="vetter">Tech professionals</div>
                <div class="process-tab" data-tab="candidate">Members</div>
            </div>

            <div class="process-content active" id="vetter-process">
                <div class="steps">
                    <div class="step">
                        <div class="icon">📋</div>
                        <h3>1. Apply as a verifier</h3>
                        <p>Sign up to the community, submit your traditional CV and areas of expertise for
                            review</p>
                    </div>
                    <div class="step">
                        <div class="icon">🧪</div>
                        <h3>2. Interview</h3>
                        <p>Demonstrate your expertise and pass the interview to
                            a become verifier </p>
                    </div>
                    <div class="step">
                        <div class="icon">💬</div>
                        <h3>3. Start verifying</h3>
                        <p>Access our AI-assisted dashboard to easily review candidate submissions</p>
                    </div>
                    <div class="step">
                        <div class="icon">💰</div>
                        <h3>4. Earn</h3>
                        <p>Earn income by selling your credibility. Grow your
                            expertise by learning from candidate
                            feedback.</p>
                    </div>
                </div>
            </div>

            <div class="process-content" id="candidate-process">
                <div class="steps">
                    <div class="step">
                        <div class="icon">📝</div>
                        <h3>1. Create profile</h3>
                        <p>Sign up to the community, upload your traditional CV
                            and select the skills you want to verify
                        </p>
                    </div>
                    <div class="step">
                        <div class="icon">🧠</div>
                        <h3>2. Complete assignment</h3>
                        <p>Complete a short take at home assignment that demonstrates your
                            skills. Our take at home assignment are designed to
                            surface your true skills</p>
                    </div>
                    <div class="step">
                        <div class="icon">💬</div>
                        <h3>3. Technical discussion</h3>
                        <p>If your solution is picked by a verifier, you will have a
                            technical discussion with the verifier to validate your
                            skills and knowledge</p>
                    </div>
                    <div class="step">
                        <div class="icon">✅</div>
                        <h3>4. Final conclusion</h3>
                        <p>If you pass the technical discussion, you will receive your verified
                            skill badge and get a chance to rate the verifier</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CV2.0 Preview Section -->
    <section class="cv-preview">
        <div class="container">
            <h2>Your Digital CV2.0 - Verified by the Community</h2>
            <p class="reputation-intro">See what makes our verified CV different from traditional
                resumes. Our flexible CV2.0 platform lets you showcase your verified skills
                in a format that best represents your expertise and achievements.
                You can hide your community status or display it proudly, you can
                hide skills badges and other tons of customizations.
                This is just one of many CV2.0 templates used to display your
                verified skills
            </p>
            <p class="reputation-intro"> Note: This is not going to be the final version, our CV2.0 platform
                is heavily in development and is going to be much more advanced and
                customizable.</p>

            <div class="cv-showcase">
                <div class="cv-header">
                    <div class="profile-img">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="profile-info">
                        <h2>Alex Johnson</h2>
                        <p>Senior Full Stack Developer</p>
                    </div>
                </div>

                <div class="cv-body">
                    <div class="left-column">
                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-user-tie"></i> Profile</h3>
                            <p class="item-description">Full stack developer with 8+ years of experience building
                                scalable web applications. Passionate about clean code and innovative solutions.</p>
                        </div>

                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-code"></i> Technical Skills</h3>
                            <div class="skills-grid">
                                <div class="skill">
                                    <span class="skill-name">JavaScript</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by Sarah Chen
                                        <div class="verifier-info">Senior Developer at Google</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">React</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by Michael Rodriguez
                                        <div class="verifier-info">Engineering Lead at Meta</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">Node.js</span>
                                    <span class="unverified-badge"><i class="fas fa-clock"></i> Unverified</span>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">Python</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by James Wilson
                                        <div class="verifier-info">Principal Engineer at Netflix</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">AWS</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by Emily Zhang
                                        <div class="verifier-info">Cloud Architect at Amazon</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">Docker</span>
                                    <span class="unverified-badge"><i class="fas fa-clock"></i> Pending</span>
                                </div>
                            </div>
                        </div>

                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-graduation-cap"></i> Education</h3>
                            <div class="education-item">
                                <div class="item-header">
                                    <span class="item-title">MSc Computer Science</span>
                                    <span class="item-date">2016</span>
                                </div>
                                <div class="item-subtitle">Stanford University</div>
                                <span class="verification-status status-pending"><i class="fas fa-clock"></i>
                                    Pending</span>
                            </div>
                            <div class="education-item">
                                <div class="item-header">
                                    <span class="item-title">BSc Software Engineering</span>
                                    <span class="item-date">2014</span>
                                </div>
                                <div class="item-subtitle">MIT</div>
                                <span class="verification-status status-pending"><i class="fas fa-clock"></i>
                                    Pending</span>
                            </div>
                        </div>

                        <!-- Projects Section -->
                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-project-diagram"></i> Projects</h3>
                            <div class="project-item">
                                <div class="item-header">
                                    <span class="item-title">Real-Time Object Detection System</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                </div>
                                <div class="item-subtitle">Python, OpenCV, YOLOv5</div>
                                <p class="item-description">Developed a real-time object detection system using YOLOv5
                                    that achieves 95% accuracy on the COCO dataset. Optimized inference speed to 30 FPS
                                    on NVIDIA GTX 1080Ti.</p>
                                <div class="skill-tooltip">
                                    Verified by Michael Rodriguez
                                    <div class="verifier-info">Computer Vision Engineer at Tesla</div>
                                </div>
                            </div>

                            <div class="project-item">
                                <div class="item-header">
                                    <span class="item-title">AI-Powered Document Scanner</span>
                                    <span class="unverified-badge"><i class="fas fa-clock"></i> Pending</span>
                                </div>
                                <div class="item-subtitle">Python, OpenCV, TensorFlow</div>
                                <p class="item-description">Building a mobile document scanner with perspective
                                    correction and OCR capabilities using OpenCV and Tesseract.</p>
                            </div>

                            <div class="project-item">
                                <div class="item-header">
                                    <span class="item-title">Face Recognition Attendance System</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                </div>
                                <div class="item-subtitle">Python, Dlib, Face Recognition API</div>
                                <p class="item-description">Created a facial recognition system that automatically
                                    records attendance with 99.2% accuracy in varied lighting conditions.</p>
                                <div class="skill-tooltip">
                                    Verified by Emily Zhang
                                    <div class="verifier-info">AI Researcher at Microsoft</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="right-column">
                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-briefcase"></i> Experience</h3>
                            <div class="experience-item">
                                <div class="item-header">
                                    <span class="item-title">Senior Developer</span>
                                    <span class="item-date">2020 - Present</span>
                                </div>
                                <div class="item-subtitle">TechInnovate Inc.</div>
                                <p class="item-description">Lead development of customer-facing web applications using
                                    React and Node.js. Mentored junior developers and implemented CI/CD pipelines.</p>
                                <span class="verification-status status-unverified"><i class="fas fa-clock"></i>
                                    Unverified</span>
                            </div>
                            <div class="experience-item">
                                <div class="item-header">
                                    <span class="item-title">Full Stack Developer</span>
                                    <span class="item-date">2017 - 2020</span>
                                </div>
                                <div class="item-subtitle">WebSolutions LLC</div>
                                <p class="item-description">Developed and maintained multiple web applications for
                                    clients across various industries. Implemented responsive designs and optimized
                                    performance.</p>
                                <span class="verification-status status-verified"><i class="fas fa-check"></i>
                                    Verified</span>
                            </div>
                            <div class="experience-item">
                                <div class="item-header">
                                    <span class="item-title">Junior Developer</span>
                                    <span class="item-date">2015 - 2017</span>
                                </div>
                                <div class="item-subtitle">DigitalCreations</div>
                                <p class="item-description">Assisted in development of front-end interfaces and backend
                                    APIs. Participated in code reviews and agile development processes.</p>
                                <span class="verification-status status-unverified"><i class="fas fa-clock"></i>
                                    Unverified</span>
                            </div>
                        </div>

                        <div class="cv-section">
                            <h3 class="cv-section-title"><i class="fas fa-medal"></i> Certifications</h3>
                            <div class="skills-grid">
                                <div class="skill">
                                    <span class="skill-name">AWS Certified</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by David Kim
                                        <div class="verifier-info">Solutions Architect at Amazon</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">React Expert</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by Lisa Johnson
                                        <div class="verifier-info">Frontend Lead at Meta</div>
                                    </div>
                                </div>
                                <div class="skill">
                                    <span class="skill-name">Node.js Master</span>
                                    <span class="verified-badge"><i class="fas fa-check"></i> Verified</span>
                                    <div class="skill-tooltip">
                                        Verified by Robert Williams
                                        <div class="verifier-info">Backend Engineer at Netflix</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-section">
                    <div class="qr-code">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <p>Scan to view my verified skills</p>
                    <a href="#" class="verification-link">View full verification history</a>
                </div>
            </div>

            <div class="cv-features">
                <div class="cv-feature">
                    <i class="fas fa-qrcode"></i>
                    <h3>QR Verification</h3>
                    <p>Employers can scan QR codes to verify credential authenticity instantly</p>
                </div>
                <div class="cv-feature">
                    <i class="fas fa-shield-alt"></i>
                    <h3>Skill Badges</h3>
                    <p>Visual badges show exactly which skills have been
                        verified by the community</p>
                </div>
                <div class="cv-feature">
                    <i class="fas fa-database"></i>
                    <h3>Optimized metadata</h3>
                    <p>Every exported CV2.0 is optimized for HR AIs to read and understand your skills</p>
                </div>
                <div class="cv-feature">
                    <i class="fas fa-link"></i>
                    <h3>Live Links</h3>
                    <p>Direct links to your verified projects and code repositories</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq" id="faq">
        <div class="container">
            <h2>Frequently asked questions for members</h2>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        How does the verification process work? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Our verification process involves a multi-step evaluation where your skills are assessed by
                            experienced tech professionals in the field. Our tech
                            professionals come from different backgrounds and
                            companies like Google, Netflix, Airbnb, Amazon, Meta and many more.
                            Once verified, your skills are permanently recorded and
                            can be easily validated by employers through QR codes or direct links.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        How long does it take to get verified? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>The verification process typically takes 3-5 business days, depending on the complexity of
                            the skills being assessed and the availability of our verifiers.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Can I update my skills after verification? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, you can always add new skills or request re-verification of existing skills as you
                            continue to develop your expertise. The platform is designed to grow with your career.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        How do employers verify my skills? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Employers can verify your skills by scanning the QR code on your digital CV or by visiting
                            your unique profile URL. They'll see a complete verification history with timestamps and
                            reviewer credentials.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        I am not happy with my verification outcome, what can I do? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>You can always submit a complaint if you think you
                            have not been treated fairly. We will investigate your
                            complaint, if your claim turns out to be true, we will
                            assign a new verifier to your case.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Can I become a verifier as well and earn? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Of course you can, after you have verified the skills
                            you want to verify other members skills with, you can apply
                            and go through the become a verifier process.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Is my data and code safe? Who can see my verification results? <i
                            class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Your privacy is paramount. Your specific code and assignment solutions are only visible to
                            the verifiers assigned to you. Your public profile and CV2.0 will only show the skills you
                            choose to make public and their verification status. You have full control over your
                            visibility settings.</p>
                    </div>
                </div>


            </div>
        </div>
    </section>
    <section class="faq" id="faq">
        <div class="container">
            <h2>Frequently asked questions for verifiers</h2>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        How much can I earn as a verifier? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Earnings are based on a share of the verification fee for each assessment you complete. The
                            more complex the skill, the higher the fee and your potential earnings. We provide a clear
                            breakdown of potential earnings in your verifier dashboard.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        What's the time commitment for being a verifier? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>There is no minimum time commitment. You can choose
                            to take on as many or as few verification
                            tasks as your schedule allows. The platform is designed for flexibility.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        How does the "AI-assisted dashboard" help me? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Our dashboard helps you streamline the review process. It can help with initial code
                            analysis, flagging common patterns, managing your queue of assignments, and generating
                            structured feedback templates, allowing you to focus on the high-level assessment.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        What happens if a candidate disputes my assessment? <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Our admin team will review the dispute neutrally. If your feedback is found to be
                            constructive, professional, and accurate, your rating and standing will be unaffected. This
                            system protects verifiers from frivolous disputes while ensuring fairness for candidates.
                        </p>
                    </div>
                </div>
            </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta" id="waitlist">
        <div class="container">
            <h2>Tired of your job applications getting ignored?</h2>
            <p>Join our community to be among the first to access SkillVerdict
                and see how it works</p>

            <div style="margin-top: 30px; display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="#for-vetters" class="cta-button">Verify my skills</a>
                <a href="#for-candidates" class="cta-button">Earn by verifying</a>
            </div>
            <form class="signup-form">
                <input type="email" class="form-input" placeholder="Your email address *" required>
                <input type="url" class="form-input" placeholder="Your LinkedIn profile URL *" required>
                <textarea class="form-input form-textarea"
                    placeholder="Biggest pain point with job hunting (optional)"></textarea>
                <button type="submit" class="submit-button">Secure My Spot</button>
            </form>

        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>© 2024 SkillVerdict. All rights reserved.</p>
            <p><a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            <div class="social-links">
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-linkedin"></i></a>
                <a href="#"><i class="fab fa-github"></i></a>
            </div>
        </div>
    </footer>

    <script>
        // Simple form submission handling
        document.querySelectorAll('.signup-form').forEach(form => {
            form.addEventListener('submit', function (e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;
                const linkedin = this.querySelector('input[type="url"]').value;

                if (email && linkedin) {
                    alert(`Thank you! We've added ${email} to our waitlist. You'll be among the first to know when we launch!`);
                    this.reset();
                }
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Process tabs functionality
        const processTabs = document.querySelectorAll('.process-tab');
        const processContents = document.querySelectorAll('.process-content');

        processTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');

                // Update active tab
                processTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Show relevant content
                processContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${tabId}-process`) {
                        content.classList.add('active');
                    }
                });
            });
        });

        // FAQ accordion functionality
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach(question => {
            question.addEventListener('click', () => {
                const item = question.parentElement;

                // Close other FAQ items
                document.querySelectorAll('.faq-item').forEach(faqItem => {
                    if (faqItem !== item) {
                        faqItem.classList.remove('active');
                    }
                });

                // Toggle current item
                item.classList.toggle('active');
            });
        });

        // Two Sides Section Tabs
        const twoSidesTabs = document.querySelectorAll('.two-sides .tab');
        const twoSidesTabContents = document.querySelectorAll('.two-sides .tab-content');

        twoSidesTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');

                // Update active tab
                twoSidesTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Show relevant content
                twoSidesTabContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${tabId}-content`) {
                        content.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>

</html>